import { Injectable, Logger } from '@nestjs/common';
import { DrizzleService } from '../drizzle/drizzle.service';

export interface RetryOptions {
  maxAttempts?: number;
  baseDelayMs?: number;
  maxDelayMs?: number;
  timeoutMs?: number;
  exponentialBackoff?: boolean;
  jitter?: boolean;
  retryCondition?: (error: any) => boolean;
}

export interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  attempts: number;
  totalDuration: number;
}

export interface BatchRetryResult<T> {
  successes: Array<{ id: string; data: T }>;
  failures: Array<{ id: string; error: string; attempts: number }>;
  totalProcessed: number;
  totalDuration: number;
}

@Injectable()
export class DatabaseRetryService {
  private readonly logger = new Logger(DatabaseRetryService.name);

  constructor(private readonly drizzle: DrizzleService) {}

  /**
   * Execute a database operation with retry logic and timeout handling
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    options: RetryOptions = {},
  ): Promise<RetryResult<T>> {
    const {
      maxAttempts = 3,
      baseDelayMs = 1000,
      maxDelayMs = 10000,
      timeoutMs = 30000,
      exponentialBackoff = true,
      jitter = true,
      retryCondition = this.defaultRetryCondition,
    } = options;

    const startTime = Date.now();
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        // Wrap operation with timeout
        const result = await Promise.race([
          operation(),
          new Promise<never>((_, reject) =>
            setTimeout(
              () => reject(new Error(`Operation timeout after ${timeoutMs}ms`)),
              timeoutMs,
            ),
          ),
        ]);

        const totalDuration = Date.now() - startTime;
        this.logger.debug(
          `Operation succeeded on attempt ${attempt}/${maxAttempts} after ${totalDuration}ms`,
        );

        return {
          success: true,
          data: result,
          attempts: attempt,
          totalDuration,
        };
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        const shouldRetry = attempt < maxAttempts && retryCondition(lastError);
        
        this.logger.warn(
          `Operation failed on attempt ${attempt}/${maxAttempts}: ${lastError.message}`,
          {
            error: lastError.message,
            willRetry: shouldRetry,
            attempt,
            maxAttempts,
          },
        );

        if (!shouldRetry) {
          break;
        }

        // Calculate delay with exponential backoff and jitter
        const delay = this.calculateDelay(
          attempt,
          baseDelayMs,
          maxDelayMs,
          exponentialBackoff,
          jitter,
        );

        this.logger.debug(`Waiting ${delay}ms before retry attempt ${attempt + 1}`);
        await this.sleep(delay);
      }
    }

    const totalDuration = Date.now() - startTime;
    return {
      success: false,
      error: lastError!,
      attempts: maxAttempts,
      totalDuration,
    };
  }

  /**
   * Execute batch operations with individual retry logic
   */
  async executeBatchWithRetry<T>(
    items: Array<{ id: string; operation: () => Promise<T> }>,
    options: RetryOptions = {},
    batchSize: number = 10,
  ): Promise<BatchRetryResult<T>> {
    const startTime = Date.now();
    const successes: Array<{ id: string; data: T }> = [];
    const failures: Array<{ id: string; error: string; attempts: number }> = [];

    this.logger.log(
      `Starting batch operation with ${items.length} items, batch size: ${batchSize}`,
    );

    // Process items in batches to avoid overwhelming the database
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      
      this.logger.debug(
        `Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(items.length / batchSize)} (${batch.length} items)`,
      );

      // Process batch items in parallel with individual retry logic
      const batchPromises = batch.map(async (item) => {
        const result = await this.executeWithRetry(item.operation, options);
        
        if (result.success) {
          successes.push({ id: item.id, data: result.data! });
        } else {
          failures.push({
            id: item.id,
            error: result.error!.message,
            attempts: result.attempts,
          });
        }
      });

      await Promise.all(batchPromises);

      // Add small delay between batches to reduce database load
      if (i + batchSize < items.length) {
        await this.sleep(100);
      }
    }

    const totalDuration = Date.now() - startTime;
    
    this.logger.log(
      `Batch operation completed: ${successes.length} successes, ${failures.length} failures in ${totalDuration}ms`,
    );

    return {
      successes,
      failures,
      totalProcessed: items.length,
      totalDuration,
    };
  }

  /**
   * Execute chunked operations from a file like chunked-uuids.json
   */
  async executeChunkedOperations<T>(
    chunks: string[][],
    operationFactory: (userIds: string[]) => Promise<T>,
    options: RetryOptions = {},
  ): Promise<BatchRetryResult<T>> {
    const startTime = Date.now();
    const successes: Array<{ id: string; data: T }> = [];
    const failures: Array<{ id: string; error: string; attempts: number }> = [];

    this.logger.log(`Processing ${chunks.length} chunks`);

    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
      const chunk = chunks[chunkIndex];
      const chunkId = `chunk_${chunkIndex + 1}`;
      
      this.logger.debug(
        `Processing chunk ${chunkIndex + 1}/${chunks.length} with ${chunk.length} items`,
      );

      const result = await this.executeWithRetry(
        () => operationFactory(chunk),
        {
          ...options,
          timeoutMs: options.timeoutMs || 60000, // Longer timeout for chunks
        },
      );

      if (result.success) {
        successes.push({ id: chunkId, data: result.data! });
        this.logger.log(`Chunk ${chunkIndex + 1} completed successfully`);
      } else {
        failures.push({
          id: chunkId,
          error: result.error!.message,
          attempts: result.attempts,
        });
        this.logger.error(
          `Chunk ${chunkIndex + 1} failed after ${result.attempts} attempts: ${result.error!.message}`,
        );
      }

      // Add delay between chunks to reduce database load
      if (chunkIndex < chunks.length - 1) {
        await this.sleep(500);
      }
    }

    const totalDuration = Date.now() - startTime;
    const totalItems = chunks.reduce((sum, chunk) => sum + chunk.length, 0);

    this.logger.log(
      `Chunked operation completed: ${successes.length}/${chunks.length} chunks succeeded, ${totalItems} total items processed in ${totalDuration}ms`,
    );

    return {
      successes,
      failures,
      totalProcessed: chunks.length,
      totalDuration,
    };
  }

  /**
   * Default retry condition - retries on timeout and connection errors
   */
  private defaultRetryCondition(error: Error): boolean {
    const message = error.message.toLowerCase();
    
    // Retry on these error types
    const retryableErrors = [
      'timeout',
      'connection',
      'econnrefused',
      'enotfound',
      'etimedout',
      'query read timeout',
      'connection terminated',
      'connection lost',
      'pool exhausted',
      'too many connections',
    ];

    return retryableErrors.some(errorType => message.includes(errorType));
  }

  /**
   * Calculate delay with exponential backoff and jitter
   */
  private calculateDelay(
    attempt: number,
    baseDelayMs: number,
    maxDelayMs: number,
    exponentialBackoff: boolean,
    jitter: boolean,
  ): number {
    let delay = exponentialBackoff
      ? Math.min(baseDelayMs * Math.pow(2, attempt - 1), maxDelayMs)
      : baseDelayMs;

    if (jitter) {
      // Add ±25% jitter to prevent thundering herd
      const jitterRange = delay * 0.25;
      delay += (Math.random() - 0.5) * 2 * jitterRange;
    }

    return Math.max(delay, 100); // Minimum 100ms delay
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
