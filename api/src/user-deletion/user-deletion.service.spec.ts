import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { UserDeletionService } from './user-deletion.service';
import { DrizzleService } from '@app/shared/drizzle/drizzle.service';
import { EmailService } from '../mail/email.service';
import { QueueService } from '@app/shared/queue/queue.service';

describe('UserDeletionService', () => {
  let service: UserDeletionService;
  let mockDrizzleService: any;
  let mockEmailService: any;
  let mockQueueService: any;

  const mockUserId = '123e4567-e89b-12d3-a456-426614174000';
  const mockUser = {
    id: mockUserId,
    email: '<EMAIL>',
    role: 'student',
    state: 'active',
    deleted: false,
    student_profile: {
      id: 'profile-id',
      first_name: 'Test',
      last_name: 'User',
    },
    profile: null,
  };

  beforeEach(async () => {
    const mockTxDelete = jest.fn().mockReturnValue({
      where: jest.fn().mockReturnValue({
        returning: jest.fn().mockReturnValue([mockUser]),
      }),
    });

    const mockTxUpdate = jest.fn().mockReturnValue({
      set: jest.fn().mockReturnValue({
        where: jest.fn().mockReturnValue({
          execute: jest.fn().mockResolvedValue([]),
        }),
      }),
    });

    const mockTransaction = jest.fn().mockImplementation(async (callback) => {
      const mockTx = {
        select: jest.fn().mockReturnValue({
          from: jest.fn().mockReturnValue({
            where: jest.fn().mockReturnValue([]), // Empty arrays for cleanup stats
          }),
        }),
        delete: mockTxDelete,
        update: mockTxUpdate,
      };
      return callback(mockTx);
    });

    mockDrizzleService = {
      db: {
        query: {
          users: {
            findFirst: jest
              .fn()
              .mockResolvedValue({ email: '<EMAIL>' }),
          },
        },
        select: jest.fn().mockImplementation((columns) => {
          // If selecting just ID (for validation)
          if (columns && columns.id) {
            return {
              from: jest.fn().mockReturnValue({
                where: jest.fn().mockReturnValue({
                  limit: jest.fn().mockResolvedValue([{ id: mockUserId }]),
                }),
              }),
            };
          }
          // If selecting email (for admin users)
          if (columns && columns.email) {
            return {
              from: jest.fn().mockReturnValue({
                where: jest
                  .fn()
                  .mockResolvedValue([
                    { email: '<EMAIL>' },
                    { email: '<EMAIL>' },
                  ]),
              }),
            };
          }
          // Default fallback
          return {
            from: jest.fn().mockReturnValue({
              where: jest.fn().mockResolvedValue([]),
            }),
          };
        }),
        transaction: mockTransaction,
        delete: jest.fn(),
        update: jest.fn(),
        execute: jest.fn().mockResolvedValue({
          rows: [
            {
              constraint_name: 'questions_created_by_users_id_fk',
              constraint_type: 'FOREIGN KEY',
              delete_rule: 'SET NULL',
              update_rule: 'NO ACTION',
            },
          ],
        }),
      },
    };

    mockEmailService = {
      sendCustomEmail: jest.fn().mockResolvedValue(true),
    };

    mockQueueService = {
      addSingleEmailJob: jest.fn().mockResolvedValue(true),
      addBulkEmailJob: jest.fn().mockResolvedValue(true),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserDeletionService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: QueueService,
          useValue: mockQueueService,
        },
      ],
    }).compile();

    service = module.get<UserDeletionService>(UserDeletionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('deleteUser', () => {
    it('should successfully delete a user', async () => {
      const result = await service.deleteUser(mockUserId);

      expect(result.success).toBe(true);
      expect(result.deletedUser).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
      });
    });

    it('should throw NotFoundException when user does not exist', async () => {
      const mockEmptyResult: any[] = [];
      mockDrizzleService.db.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue(mockEmptyResult),
          }),
        }),
      });

      await expect(service.deleteUser(mockUserId)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('deleteUserWithQuestionReassignment', () => {
    it('should delete user and handle question reassignment', async () => {
      const result = await service.deleteUserWithQuestionReassignment(
        mockUserId,
        '<EMAIL>',
        true,
        '123e4567-e89b-12d3-a456-************', // Valid UUID for admin
      );

      expect(result.success).toBe(true);
      expect(result.deletedUser).toEqual({
        id: mockUser.id,
        email: mockUser.email,
        role: mockUser.role,
      });
    });

    it('should throw error for invalid user ID', async () => {
      await expect(
        service.deleteUserWithQuestionReassignment('invalid-uuid'),
      ).rejects.toThrow(BadRequestException);
    });
  });
});
